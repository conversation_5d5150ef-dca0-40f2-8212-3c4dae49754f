.maskEditorContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 15px;
  border: 1px solid #dddddd;
  padding: 10px;
  border-radius: 4px;
}

.controlsContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.brushControls {
  display: flex;
  align-items: center;
  gap: 10px;

  label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
  }

  input[type="range"] {
    width: 100px;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;

    &::-webkit-slider-thumb {
      appearance: none;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: #3949ac;
      cursor: pointer;
    }

    &::-moz-range-thumb {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: #3949ac;
      cursor: pointer;
      border: none;
    }
  }

  span {
    font-size: 12px;
    color: #666;
    min-width: 35px;
  }

  .brushSizeIndicator {
    width: var(--brush-size, 20px);
    height: var(--brush-size, 20px);
    border-radius: 50%;
    background-color: rgba(57, 73, 172, 0.3);
    border: 2px solid #3949ac;
    display: inline-block;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: #3949ac;
    }
  }
}

.buttonControls {
  display: flex;
  gap: 10px;

  :global(.btn) {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }

    &:active {
      transform: translateY(1px);
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }
  }
}

.canvasContainer {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background-color: #f9f9f9;
  border-radius: 4px;
  overflow: hidden;

  :global(.spinner) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }
}

.drawingCanvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: crosshair;
  z-index: 2;
  touch-action: none; /* Prevent scrolling on touch devices */

  &:active {
    cursor: grabbing;
  }
}

.maskCanvas {
  display: none; /* Hidden canvas used for storing the actual mask */
}

.hiddenImage {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
}

.instructions {
  margin-top: 10px;
  font-size: 12px;
  color: #666;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 3px solid #3949ac;
}

@media (max-width: 768px) {
  .controlsContainer {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .buttonControls {
    width: 100%;
    justify-content: space-between;
  }
}
