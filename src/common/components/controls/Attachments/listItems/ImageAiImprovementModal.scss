.centerBox {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-evenly;
}
.textBottom {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.questionTop {
  display: flex;
  flex-direction: row;
  width: 100%;
  flex-wrap: wrap;
  justify-content: space-evenly;
}
.questionBox {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.border {
  border: 1px solid #dddddd;
}
.flex {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}
.flexClear {
  display: flex;
  justify-content: space-between;
  margin: 0 -10px
}
.flexTop {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}
.flexOption {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.captionBox {
  :global(.captionLabel) {
    padding: 0px !important;
    margin: 0px !important;
  }
}
.flexCol {
  display: flex;
  flex-direction: column;
}
.description {
  white-space: pre-wrap;
}
.center {
  text-align: center;
}
.noSpace {
  padding: 0px !important;
  :global(.form-group) {
    padding: 0px !important;
    margin: 0px !important;
  }
}
.autoTextBox {
  :global(.form-group) {
    margin-bottom: 20px !important;
  }
}

.count {
  float: left;
  background: #dddddd;
  padding: 4px 8px;
  border-radius: 20%;
  margin: 6px;
}
.textScroll {
  max-height: 350px;
  overflow-y: scroll;
}
.rubricTextArea {
  resize: none;
  border-bottom: none;
  margin: 0px;
}
.checkBox {
  vertical-align: top !important;
}
.mb50 {
  margin-bottom: 50px;
}
.flexRow {
  display: flex;
  flex-direction: row;
}
.textListAuto {
  min-width: 350px;
}
.showMobile {
  display: none;
}
@media screen and (max-width: 768px) {
  .hideMobile {
    display: none !important;
  }
  .showMobile {
    display: block !important;
  }

  .questionTop {
    flex-direction: column;
  }

  .col_40, .col_10, .col_50 {
    width: 100% !important;
    margin-bottom: 15px;
  }

  .h60 {
    min-height: 25vh !important;
  }
}
@media screen and (max-width: 500px) {
  .textListAuto {
    min-width: 40vw !important;
  }
}
.active {
  background-color: #3949ac;
  :global(.notification-counts) {
    color: white !important;
    font-weight: bold;
    font-size: 13px;
    max-width: 50px;
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.inactive {
  background-color: #ffffff;
  border: 1px solid #3949ac;
  :global(.notification-counts) {
    color: #3949ac !important;
    font-weight: bold;
    font-size: 13px;
    max-width: 50px;
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.buttonCustom2 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.ml {
  margin-left: 0px !important;
}
.h70 {
  min-height: 58vh !important;
}
.h60 {
  min-height: 30vh !important; /* Reduced height to accommodate mask editor */
}
.h23 {
  min-height: 23vh !important;
}
.h25 {
  min-height: 25vh !important;
}
.h16 {
  min-height: 16vh !important;
}
.h32 {
  min-height: 32vh !important;
}
.h20 {
  min-height: 20vh !important;
}
.h45 {
  min-height: 45vh !important;
}
.h40 {
  min-height: 40vh !important;
}
@media (min-width: 1280px) {
  .col_10 {
    width: 15% !important;
  }
  .col_40 {
    width: 40% !important;
    min-height: 600px; /* Increased height to accommodate mask editor */
  }
  .col_50 {
    width: 45% !important;
  }
}
.modalHr {
  border-top: 1px solid #f7f7f7 !important;
}

.sectionTitle {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: flex;
  flex-direction: column;
  position: relative;

  .maskActiveIndicator {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #4caf50;
    color: white;
    font-size: 10px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
  }

  .sectionSubtitle {
    font-weight: 400;
    font-size: 12px;
    color: #666;
    margin-top: 2px;
  }
}

.maskEditor {
  margin-bottom: 15px;

  @media (max-width: 768px) {
    margin-bottom: 10px;

    :global(.maskEditorContainer) {
      padding: 8px;
    }
  }
}