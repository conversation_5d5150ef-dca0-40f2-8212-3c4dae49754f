import React, { useCallback, useEffect, useRef, useState } from 'react';
import classNames from 'classnames';
import styles from './ImageMaskEditor.scss';
import useT from '../../../utils/Translations/useT';
import RoundedLinkButton from '../../RoundedLinkButton';
import Spinner from '../../../utils/Spinner';

interface IImageMaskEditor {
  imageUrl: string;
  onMaskChange: (maskDataUrl: string | null) => void;
  className?: string;
}

const ImageMaskEditor: React.FC<IImageMaskEditor> = ({
  imageUrl,
  onMaskChange,
  className,
}) => {
  const t = useT();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const maskCanvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [brushSize, setBrushSize] = useState(20);
  const [showMask, setShowMask] = useState(false);

  // Initialize canvas when image loads
  useEffect(() => {
    const image = imageRef.current;
    if (!image) return;

    setIsLoading(true);
    
    const handleImageLoad = () => {
      const canvas = canvasRef.current;
      const maskCanvas = maskCanvasRef.current;
      if (!canvas || !maskCanvas || !image) return;

      // Set canvas dimensions to match image
      canvas.width = image.naturalWidth;
      canvas.height = image.naturalHeight;
      maskCanvas.width = image.naturalWidth;
      maskCanvas.height = image.naturalHeight;

      // Initialize mask canvas with black (transparent in mask)
      const maskCtx = maskCanvas.getContext('2d');
      if (maskCtx) {
        maskCtx.fillStyle = 'black';
        maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height);
      }

      setIsLoading(false);
    };

    image.onload = handleImageLoad;
    
    // If image is already loaded
    if (image.complete) {
      handleImageLoad();
    }
  }, [imageUrl]);

  // Track previous point for smooth drawing
  const prevPointRef = useRef<{ x: number; y: number } | null>(null);

  // Get coordinates from mouse or touch event
  const getCoordinates = useCallback(
    (
      e: React.MouseEvent | React.TouchEvent | TouchEvent,
      canvas: HTMLCanvasElement,
    ) => {
      const rect = canvas.getBoundingClientRect();
      const scaleX = canvas.width / rect.width;
      const scaleY = canvas.height / rect.height;

      let clientX, clientY;

      // Handle both mouse and touch events
      if ('touches' in e) {
        clientX = e.touches[0].clientX;
        clientY = e.touches[0].clientY;
      } else {
        clientX = e.clientX;
        clientY = e.clientY;
      }

      return {
        x: (clientX - rect.left) * scaleX,
        y: (clientY - rect.top) * scaleY,
      };
    },
    [],
  );

  // Drawing functions
  const startDrawing = useCallback(
    (
      e:
        | React.MouseEvent<HTMLCanvasElement>
        | React.TouchEvent<HTMLCanvasElement>,
    ) => {
      e.preventDefault(); // Prevent scrolling on touch devices
      setIsDrawing(true);

      const canvas = canvasRef.current;
      if (!canvas) return;

      const point = getCoordinates(e, canvas);
      prevPointRef.current = point;
      drawPoint(point.x, point.y);
    },
    [getCoordinates],
  );

  const stopDrawing = useCallback(() => {
    setIsDrawing(false);
    prevPointRef.current = null;

    // Update mask data and notify parent
    if (maskCanvasRef.current) {
      const maskDataUrl = maskCanvasRef.current.toDataURL('image/png');
      onMaskChange(maskDataUrl);
    }
  }, [onMaskChange]);

  // Update the visible canvas with the current mask
  const updateVisibleCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const maskCanvas = maskCanvasRef.current;
    if (!canvas || !maskCanvas) return;

    const visibleCtx = canvas.getContext('2d');
    if (!visibleCtx || !imageRef.current) return;

    visibleCtx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw the original image
    visibleCtx.drawImage(imageRef.current, 0, 0, canvas.width, canvas.height);

    // Overlay the mask with semi-transparency
    visibleCtx.globalAlpha = 0.5;
    visibleCtx.drawImage(maskCanvas, 0, 0);
    visibleCtx.globalAlpha = 1.0;
  }, []);

  // Draw a single point
  const drawPoint = useCallback(
    (x: number, y: number) => {
      const maskCanvas = maskCanvasRef.current;
      if (!maskCanvas) return;

      const ctx = maskCanvas.getContext('2d');
      if (!ctx) return;

      const FULL_CIRCLE = Math.PI * 2;

      ctx.fillStyle = 'white'; // White areas are the mask
      ctx.beginPath();
      ctx.arc(x, y, brushSize, 0, FULL_CIRCLE);
      ctx.fill();

      updateVisibleCanvas();
    },
    [brushSize, updateVisibleCanvas],
  );

  // Draw a line between two points for smooth drawing
  const drawLine = useCallback(
    (from: { x: number; y: number }, to: { x: number; y: number }) => {
      const maskCanvas = maskCanvasRef.current;
      if (!maskCanvas) return;

      const ctx = maskCanvas.getContext('2d');
      if (!ctx) return;

      const BRUSH_MULTIPLIER = 2;

      ctx.fillStyle = 'white';
      ctx.strokeStyle = 'white';
      ctx.lineWidth = brushSize * BRUSH_MULTIPLIER;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';

      ctx.beginPath();
      ctx.moveTo(from.x, from.y);
      ctx.lineTo(to.x, to.y);
      ctx.stroke();

      updateVisibleCanvas();
    },
    [brushSize, updateVisibleCanvas],
  );

  // Drawing functions (defined earlier)
  const startDrawing = useCallback(
    (
      e:
        | React.MouseEvent<HTMLCanvasElement>
        | React.TouchEvent<HTMLCanvasElement>,
    ) => {
      e.preventDefault(); // Prevent scrolling on touch devices
      setIsDrawing(true);

      const canvas = canvasRef.current;
      if (!canvas) return;

      const point = getCoordinates(e, canvas);
      prevPointRef.current = point;
      drawPoint(point.x, point.y);
    },
    [getCoordinates, drawPoint],
  );

  const draw = useCallback(
    (
      e:
        | React.MouseEvent<HTMLCanvasElement>
        | React.TouchEvent<HTMLCanvasElement>,
    ) => {
      e.preventDefault(); // Prevent scrolling on touch devices
      if (!isDrawing) return;

      const canvas = canvasRef.current;
      if (!canvas) return;

      const currentPoint = getCoordinates(e, canvas);

      if (prevPointRef.current) {
        // Draw a line for smooth drawing
        drawLine(prevPointRef.current, currentPoint);
      } else {
        // Just draw a point
        drawPoint(currentPoint.x, currentPoint.y);
      }

      prevPointRef.current = currentPoint;
    },
    [isDrawing, getCoordinates, drawLine, drawPoint],
  );

  const clearMask = useCallback(() => {
    const maskCanvas = maskCanvasRef.current;
    const canvas = canvasRef.current;
    if (!maskCanvas || !canvas) return;

    const maskCtx = maskCanvas.getContext('2d');
    if (maskCtx) {
      maskCtx.fillStyle = 'black';
      maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height);
    }

    // Redraw the visible canvas
    const visibleCtx = canvas.getContext('2d');
    if (visibleCtx && imageRef.current) {
      visibleCtx.clearRect(0, 0, canvas.width, canvas.height);
      visibleCtx.drawImage(imageRef.current, 0, 0, canvas.width, canvas.height);
    }

    // Notify parent that mask was cleared
    onMaskChange(null);
  }, [onMaskChange]);

  const toggleMaskView = useCallback(() => {
    setShowMask(prev => !prev);

    const canvas = canvasRef.current;
    const maskCanvas = maskCanvasRef.current;
    if (!canvas || !maskCanvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx || !imageRef.current) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (!showMask) {
      // Show only the mask
      ctx.drawImage(maskCanvas, 0, 0);
    } else {
      // Show the image with mask overlay
      ctx.drawImage(imageRef.current, 0, 0, canvas.width, canvas.height);
      ctx.globalAlpha = 0.5;
      ctx.drawImage(maskCanvas, 0, 0);
      ctx.globalAlpha = 1.0;
    }
  }, [showMask]);

  return (
    <div className={classNames(styles.maskEditorContainer, className)}>
      <div className={styles.controlsContainer}>
        <div className={styles.brushControls}>
          <label>{t('Brush Size')}: </label>
          <input
            type="range"
            min="5"
            max="50"
            value={brushSize}
            onChange={(e) => setBrushSize(parseInt(e.target.value))}
          />
          <span>{brushSize}px</span>
          <div
            className={styles.brushSizeIndicator}
            // eslint-disable-next-line react/forbid-dom-props
            style={
              {
                '--brush-size': `${brushSize}px`,
              } as React.CSSProperties
            }
          />
        </div>
        <div className={styles.buttonControls}>
          <RoundedLinkButton additionClasses="btn-xs" onClick={clearMask}>
            {t('Clear Mask')}
          </RoundedLinkButton>
          <RoundedLinkButton additionClasses="btn-xs" onClick={toggleMaskView}>
            {showMask ? t('Show Image') : t('Show Mask')}
          </RoundedLinkButton>
        </div>
      </div>

      <div className={styles.canvasContainer}>
        {isLoading && <Spinner />}
        <img
          ref={imageRef}
          alt="Original"
          className={styles.hiddenImage}
          src={imageUrl}
        />
        <canvas
          ref={canvasRef}
          className={styles.drawingCanvas}
          onMouseDown={startDrawing}
          onMouseLeave={stopDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onTouchCancel={stopDrawing}
          onTouchEnd={stopDrawing}
          onTouchMove={draw}
          onTouchStart={startDrawing}
        />
        <canvas ref={maskCanvasRef} className={styles.maskCanvas} />
      </div>
      <div className={styles.instructions}>
        {t(
          'Draw on the image to create a mask. The highlighted areas will be edited by AI.',
        )}
      </div>
    </div>
  );
};

export default ImageMaskEditor;
